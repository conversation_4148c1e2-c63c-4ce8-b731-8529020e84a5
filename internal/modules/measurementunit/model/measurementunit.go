package model

import "time"

// Unit Measurement Category models
type UnitMeasurementCategory struct {
	ID        string
	Name      string
	Code      string
	CreatedAt *time.Time
	UpdatedAt *time.Time
	DeletedAt *time.Time
}

type UnitMeasurementCategoryCreate struct {
	Name string
	Code string
}

type UnitMeasurementCategoryUpdate struct {
	ID   string
	Name string
	Code string
}

// Measurement Unit models
type MeasurementUnit struct {
	ID                        string
	Name                      string
	Code                      string
	UnitMeasurementCategoryID *string
	Abbreviation              *string
	Type                      *string
	ConversionFactor          *float64
	State                     *string
	CreatedAt                 *time.Time
	UpdatedAt                 *time.Time
	DeletedAt                 *time.Time
}

type MeasurementUnitCreate struct {
	Name                      string
	Code                      string
	UnitMeasurementCategoryID *string
	Abbreviation              *string
	Type                      *string
	ConversionFactor          *float64
	State                     *string
}

type MeasurementUnitUpdate struct {
	ID                        string
	Name                      string
	Code                      string
	UnitMeasurementCategoryID *string
	Abbreviation              *string
	Type                      *string
	ConversionFactor          *float64
	State                     *string
}
